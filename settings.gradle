pluginManagement {
    // repository url
    def privateRepository = 'https://192.168.88.30/artifactory/libs-release/'
    def privateSnapshotRepository = 'https://192.168.88.30/artifactory/libs-snapshot/'
    repositories {
        maven {
            url privateRepository
            credentials {
                username = System.getenv().artifactory2Username
                password = System.getenv().artifactory2Password
            }
        }
        maven {
            url privateSnapshotRepository
            credentials {
                username = System.getenv().artifactory2Username
                password = System.getenv().artifactory2Password
            }
        }
        gradlePluginPortal() // Default Gradle Plugin Portal
        mavenCentral()       // Maven Central Repository
        mavenLocal()
    }
}

rootProject.name = 'hammer-loyalty'
include 'hammer-endpoint-loyalty'
include 'hammer-loyalty-data-mongodb-core'
