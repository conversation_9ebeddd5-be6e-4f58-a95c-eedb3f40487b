apply plugin: 'idea'
apply plugin: 'java'
apply plugin: 'jaco<PERSON>'
apply plugin: 'java-library'
apply plugin: 'checkstyle'

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'
    options.deprecation = true
    options.compilerArgs.add("-parameters")
}


// repository url
def projectReleaseRepo = 'https://192.168.88.30/artifactory/libs-snapshot-stoken/'
def projectSnapshotRepo = 'https://192.168.88.30/artifactory/libs-release-stoken/'

// generate version base on Jenkins build number
ext.env = System.getenv();
def buildNumber = env.BUILD_NUMBER;
def cycle = env.CYCLE;
if (buildNumber) {
    if ("RELEASE" != cycle) {
        version += "-SNAPSHOT";
    }
}


java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

idea {
    module {
        downloadJavadoc = true
        downloadSources = true
    }
}


configurations.configureEach {
    resolutionStrategy {
        cacheChangingModulesFor 10, 'minutes' // Cache changing modules for 10 minutes
    }
}

checkstyle {
    toolVersion = "${checkstyle_version}"
}

afterEvaluate {
    tasks.matching { it.name.startsWith('checkstyleAot') }.configureEach {
        enabled = false
    }
}

repositories {
    maven {
        url = projectReleaseRepo
        credentials {
            username = System.getenv().artifactory2Username
            password = System.getenv().artifactory2Password
        }
    }
    maven {
        url = projectSnapshotRepo
        credentials {
            username = System.getenv().artifactory2Username
            password = System.getenv().artifactory2Password
        }
    }
    mavenCentral()
    maven { url = "https://jitpack.io" }
    maven {
        url = 'https://jaspersoft.jfrog.io/artifactory/jaspersoft-repo/'
    }
    maven {
        url = 'http://jasperreports.sourceforge.net/maven2'
        allowInsecureProtocol = true
    }
    mavenLocal()
}

jacoco {
    toolVersion = "${jacoco_version}"
}

jacocoTestReport {
    dependsOn test // tests are required to run before generating the report
    reports {
        xml.required = true
    }
}

test {
    useJUnitPlatform()
    finalizedBy jacocoTestReport // report is always generated after tests run
}